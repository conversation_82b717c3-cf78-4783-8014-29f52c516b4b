# JSON-LD Structured Data Implementation

This document explains how to use the JSON-LD structured data implementation in the HomesToCompare/Property Squares application.

## Overview

JSON-LD (JavaScript Object Notation for Linked Data) is a method of encoding linked data using JSON. It helps search engines understand your content better and can enable rich snippets in search results.

## Architecture

The implementation consists of several key components:

### 1. Static Data (`src/data/static-json-ld.js`)

- Contains website-level structured data that doesn't change frequently
- Includes Organization, WebSite, and Service schemas
- Provides helper functions for dynamic schema generation

### 2. Composable (`src/compose/useJsonLd.js`)

- Main interface for managing JSON-LD in components
- Uses Quasar's `useMeta()` for proper SSR and client-side rendering
- Provides functions for adding, removing, and updating JSON-LD
- Automatically injects schemas into the document head

### 3. SEO Component (`src/components/seo/PropertyJsonLd.vue`)

- Specialized component for property-specific JSON-LD
- Automatically manages property and breadcrumb schemas
- Reactive to property data changes

## Schema Types Implemented

### Organization Schema

- Business information for HomesToCompare/Property Squares
- Contact details and service area
- Knowledge areas and specializations

### WebSite Schema

- Website-level information
- Search functionality
- Publisher details

### Service Schema

- Real estate analysis services
- Property dossier analysis
- Price challenge games
- Neighborhood analysis

### RealEstateListing Schema

- Individual property information
- Property details (bedrooms, bathrooms, price, etc.)
- Location data and images
- Availability status

### Game Schema

- Property price challenge games
- Educational game classification
- Interaction statistics

### WebPage Schema

- Page-specific information
- Meta data from route configuration
- Breadcrumb navigation

## Usage Examples

### Basic Layout Integration

```vue
<script setup>
import useJsonLd from 'src/compose/useJsonLd.js'

const { initializeDefaultJsonLd, updateWebPageSchema } = useJsonLd()

// Initialize base schemas (Organization, WebSite, Service)
initializeDefaultJsonLd()

// Update page-specific schema
updateWebPageSchema({
  title: 'Custom Page Title',
  description: 'Custom page description',
  keywords: 'property, real estate, analysis',
})
</script>
```

### Property Page Integration

```vue
<template>
  <div>
    <!-- Include the PropertyJsonLd component -->
    <PropertyJsonLd :property="propertyData" :breadcrumbs="breadcrumbs" />

    <!-- Your property content -->
    <div>{{ propertyData.title }}</div>
  </div>
</template>

<script setup>
import PropertyJsonLd from 'src/components/seo/PropertyJsonLd.vue'

const props = defineProps({
  propertyData: Object,
})

const breadcrumbs = [
  { name: 'Home', url: '/' },
  { name: 'Properties', url: '/properties' },
  { name: props.propertyData?.title || 'Property', url: window.location.href },
]
</script>
```

### Game Page Integration

```vue
<script setup>
import useJsonLd from 'src/compose/useJsonLd.js'

const { initializeDefaultJsonLd, generateGameSessionSchema, addJsonLd } =
  useJsonLd()

// Initialize base schemas
initializeDefaultJsonLd()

// Add game-specific schema
const gameData = {
  title: 'Property Price Challenge',
  description: 'Test your property knowledge',
  totalPlayers: 1500,
}

addJsonLd(generateGameSessionSchema(gameData), 'game-schema')
</script>
```

### Manual JSON-LD Management

```vue
<script setup>
import { onMounted, onUnmounted } from 'vue'
import useJsonLd from 'src/compose/useJsonLd.js'

const { addJsonLd, removeJsonLd } = useJsonLd()

onMounted(() => {
  // Add custom schema
  const customSchema = {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: 'Property Market Analysis',
    author: {
      '@type': 'Organization',
      name: 'HomesToCompare',
    },
  }

  addJsonLd(customSchema, 'article-schema')
})

onUnmounted(() => {
  // Clean up when component unmounts
  removeJsonLd('article-schema')
})
</script>
```

## SSR Support

The implementation fully supports Server-Side Rendering using Quasar's Meta plugin:

1. **Composable**: Uses Quasar's `useMeta()` for proper SSR meta injection
2. **Automatic Injection**: JSON-LD scripts are automatically injected into the document head
3. **Components**: Work seamlessly in both SSR and client-side environments

## Route Meta Integration

JSON-LD automatically uses route meta data:

```javascript
// In your route configuration
{
  path: '/property/:id',
  component: PropertyPage,
  meta: {
    title: 'Property Details - HomesToCompare',
    description: 'Detailed property analysis and market data',
    keywords: 'property, real estate, market analysis',
    ogType: 'website',
    ogImage: '/images/property-og.jpg'
  }
}
```

## Best Practices

1. **Always Initialize**: Call `initializeDefaultJsonLd()` in layout components
2. **Use Meta Plugin**: The implementation uses Quasar's Meta plugin for proper SSR support
3. **Use Components**: Prefer the `PropertyJsonLd` component for property pages
4. **Validate**: Test your JSON-LD with Google's Rich Results Test
5. **Monitor**: Use Google Search Console to monitor rich snippet performance

## Testing

Test your JSON-LD implementation:

1. **Google Rich Results Test**: https://search.google.com/test/rich-results
2. **Schema.org Validator**: https://validator.schema.org/
3. **Browser DevTools**: Check the `<head>` section for JSON-LD scripts

## Customization

To add new schema types:

1. Add schema generators to `src/data/static-json-ld.js`
2. Extend the `useJsonLd` composable with new functions
3. Create specialized components if needed
4. Update route meta data as appropriate

## Files Created/Modified

### New Files Created:

1. `src/data/static-json-ld.js` - Static schema definitions
2. `src/compose/useJsonLd.js` - Main composable using Quasar Meta plugin
3. `src/components/seo/PropertyJsonLd.vue` - Property-specific JSON-LD component
4. `src/pages/JsonLdTestPage.vue` - Test page for validation
5. `docs/JSON-LD-IMPLEMENTATION.md` - This documentation

### Modified Files:

1. `src/concerns/psq/layouts/PsqSubdomainLayout.vue` - Added JSON-LD initialization
2. `src/concerns/realty-game/layouts/RealtyGameLayout.vue` - Added game-specific JSON-LD
3. `src/concerns/dossiers/pages/DossierDetails.vue` - Added PropertyJsonLd component
4. `src/concerns/realty-game/pages/RealtyGamePropertyPage.vue` - Added PropertyJsonLd component

## Next Steps

1. **Test Implementation**: Visit the test page at `/json-ld-test` to verify schemas
2. **Validate Schemas**: Use Google Rich Results Test and Schema.org validator
3. **Add to More Pages**: Implement JSON-LD on additional property and game pages
4. **Monitor Performance**: Use Google Search Console to track rich snippet performance
5. **Customize Business Data**: Update organization schema with actual business details
6. **Add Social Media**: Include social media profiles in organization schema
7. **Implement Reviews**: Add review/rating schemas for properties if available

## Troubleshooting

- **SSR Issues**: The implementation uses Quasar's Meta plugin for proper SSR support
- **Duplicate Schemas**: Use unique IDs when adding JSON-LD
- **Missing Data**: Check that property data is available before generating schemas
- **Validation Errors**: Use schema.org documentation for correct structure
- **Test Page**: Use `/json-ld-test` route to test and validate implementation
- **Meta Plugin**: Ensure you're using `useMeta()` from Quasar for proper injection
