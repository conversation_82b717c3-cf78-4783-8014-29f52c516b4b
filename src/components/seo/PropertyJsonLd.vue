<template>
  <div>
    <!-- This component doesn't render anything visible, it just manages JSON-LD -->
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, watch } from 'vue'
import useJsonLd from 'src/compose/useJsonLd.js'

// Define props
const props = defineProps({
  property: {
    type: Object,
    required: true
  },
  breadcrumbs: {
    type: Array,
    default: () => []
  }
})

const { addJsonLd, removeJsonLd, generatePropertySchema, addBreadcrumbSchema } = useJsonLd()

// Generate and add property JSON-LD
const addPropertyJsonLd = () => {
  if (props.property) {
    const propertySchema = generatePropertySchema(props.property)
    if (propertySchema) {
      addJsonLd(propertySchema, 'property-schema')
    }
  }

  // Add breadcrumbs if provided
  if (props.breadcrumbs && props.breadcrumbs.length > 0) {
    addBreadcrumbSchema(props.breadcrumbs)
  }
}

// Remove property JSON-LD
const removePropertyJsonLd = () => {
  removeJsonLd('property-schema')
  removeJsonLd('breadcrumb-schema')
}

// Watch for property changes
watch(() => props.property, () => {
  removePropertyJsonLd()
  addPropertyJsonLd()
}, { deep: true })

// Watch for breadcrumb changes
watch(() => props.breadcrumbs, () => {
  removeJsonLd('breadcrumb-schema')
  if (props.breadcrumbs && props.breadcrumbs.length > 0) {
    addBreadcrumbSchema(props.breadcrumbs)
  }
}, { deep: true })

onMounted(() => {
  addPropertyJsonLd()
})

onUnmounted(() => {
  removePropertyJsonLd()
})
</script>
