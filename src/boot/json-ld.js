// JSON-LD Boot File for SSR Support
// This boot file ensures JSON-LD structured data is available server-side

import { boot } from 'quasar/wrappers'
import { organizationSchema, websiteSchema, realEstateServiceSchema } from 'src/data/static-json-ld.js'

export default boot(({ app, ssrContext }) => {
  // For SSR, inject base JSON-LD schemas into the HTML head
  if (process.env.SERVER && ssrContext) {
    // Helper function to create script tag string
    const createJsonLdScriptTag = (data, id) => {
      return `<script type="application/ld+json" id="${id}">${JSON.stringify(data, null, 2)}</script>`
    }

    // Initialize base schemas that should be on every page
    const baseSchemas = [
      { data: organizationSchema, id: 'organization-schema' },
      { data: websiteSchema, id: 'website-schema' },
      { data: realEstateServiceSchema, id: 'service-schema' }
    ]

    // Add base schemas to SSR context
    if (!ssrContext.jsonLdSchemas) {
      ssrContext.jsonLdSchemas = []
    }

    baseSchemas.forEach(schema => {
      ssrContext.jsonLdSchemas.push(createJsonLdScriptTag(schema.data, schema.id))
    })

    // Helper function to inject JSON-LD into head
    const injectJsonLd = () => {
      if (ssrContext.jsonLdSchemas && ssrContext.jsonLdSchemas.length > 0) {
        const jsonLdTags = ssrContext.jsonLdSchemas.join('\n')
        
        // Inject into the head section
        if (ssrContext.rendered) {
          ssrContext.rendered = ssrContext.rendered.replace(
            '</head>',
            `${jsonLdTags}\n</head>`
          )
        }
      }
    }

    // Make the injection function available
    ssrContext.injectJsonLd = injectJsonLd
  }

  // For client-side, make JSON-LD utilities available globally
  if (!process.env.SERVER) {
    // Global JSON-LD utilities
    app.config.globalProperties.$jsonLd = {
      add: (data, id) => {
        const script = document.createElement('script')
        script.type = 'application/ld+json'
        script.innerHTML = JSON.stringify(data, null, 2)
        if (id) script.id = id
        document.head.appendChild(script)
        return script
      },
      remove: (id) => {
        const script = document.getElementById(id)
        if (script) {
          script.remove()
          return true
        }
        return false
      },
      update: (data, id) => {
        const existingScript = document.getElementById(id)
        if (existingScript) {
          existingScript.innerHTML = JSON.stringify(data, null, 2)
        } else {
          app.config.globalProperties.$jsonLd.add(data, id)
        }
      }
    }
  }
})

// Export helper functions for use in components
export const addJsonLdToSSR = (ssrContext, data, id) => {
  if (process.env.SERVER && ssrContext) {
    const scriptTag = `<script type="application/ld+json" id="${id}">${JSON.stringify(data, null, 2)}</script>`
    
    if (!ssrContext.jsonLdSchemas) {
      ssrContext.jsonLdSchemas = []
    }
    
    ssrContext.jsonLdSchemas.push(scriptTag)
  }
}

export const createJsonLdScript = (data, id = null) => {
  if (process.env.SERVER) {
    return {
      type: 'application/ld+json',
      innerHTML: JSON.stringify(data, null, 2),
      id: id || `json-ld-${Date.now()}`
    }
  } else {
    const script = document.createElement('script')
    script.type = 'application/ld+json'
    script.innerHTML = JSON.stringify(data, null, 2)
    if (id) script.id = id
    document.head.appendChild(script)
    return script
  }
}
