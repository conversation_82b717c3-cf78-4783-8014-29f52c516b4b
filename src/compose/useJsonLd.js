// Composable for managing JSON-LD structured data using Quasar Meta plugin
import { computed, reactive } from 'vue'
import { useRoute } from 'vue-router'
import { useMeta } from 'quasar'
import {
  organizationSchema,
  websiteSchema,
  realEstateServiceSchema,
  breadcrumbListSchema,
  gameSchema,
} from 'src/data/static-json-ld.js'

export default function useJsonLd() {
  const route = useRoute()

  // Reactive object to store all JSON-LD schemas
  const jsonLdSchemas = reactive({})

  // Helper function to add JSON-LD schema
  const addJsonLd = (data, id = null) => {
    const schemaId = id || `json-ld-${Date.now()}`
    jsonLdSchemas[schemaId] = JSON.stringify(data, null, 2)
  }

  // Remove JSON-LD schema
  const removeJsonLd = (id) => {
    if (jsonLdSchemas[id]) {
      delete jsonLdSchemas[id]
    }
  }

  // Clear all JSON-LD schemas
  const clearJsonLd = () => {
    Object.keys(jsonLdSchemas).forEach((key) => {
      delete jsonLdSchemas[key]
    })
  }

  // Computed property for meta script injection
  const jsonLdMeta = computed(() => {
    const scripts = {}

    Object.entries(jsonLdSchemas).forEach(([id, content]) => {
      scripts[id] = {
        type: 'application/ld+json',
        innerHTML: content,
      }
    })

    return scripts
  })

  // Use Quasar's useMeta to inject JSON-LD scripts
  useMeta(() => ({
    script: jsonLdMeta.value,
  }))

  // Generate property-specific JSON-LD
  const generatePropertySchema = (property) => {
    if (!property) return null

    return {
      '@context': 'https://schema.org',
      '@type': 'RealEstateListing',
      name: property.title || property.catchy_title || 'Property Listing',
      description:
        property.description_medium ||
        property.description_short ||
        'Property for sale',
      url: typeof window !== 'undefined' ? window.location.href : '',
      offers: {
        '@type': 'Offer',
        price: property.price_sale_current_cents
          ? (property.price_sale_current_cents / 100).toString()
          : null,
        priceCurrency: property.currency || 'GBP',
        availability: property.visible
          ? 'https://schema.org/InStock'
          : 'https://schema.org/OutOfStock',
      },
      address: {
        '@type': 'PostalAddress',
        streetAddress: property.street_address,
        addressLocality: property.city,
        addressRegion: property.region,
        postalCode: property.postal_code,
        addressCountry: property.country || 'GB',
      },
      geo:
        property.latitude && property.longitude
          ? {
              '@type': 'GeoCoordinates',
              latitude: property.latitude,
              longitude: property.longitude,
            }
          : null,
      numberOfRooms: property.count_bedrooms,
      numberOfBathroomsTotal: property.count_bathrooms,
      floorSize: property.constructed_area
        ? {
            '@type': 'QuantitativeValue',
            value: property.constructed_area,
            unitCode: property.area_unit === 'sqmt' ? 'MTK' : 'FTK',
          }
        : null,
      yearBuilt: property.year_construction || null,
      image:
        property.sale_listing_pics && property.sale_listing_pics.length > 0
          ? property.sale_listing_pics
              .map((pic) => pic.image_details?.url)
              .filter(Boolean)
          : null,
    }
  }

  // Generate game session JSON-LD
  const generateGameSessionSchema = (gameData) => {
    return {
      ...gameSchema(gameData),
      url: window.location.href,
      dateCreated: new Date().toISOString(),
      interactionStatistic: {
        '@type': 'InteractionCounter',
        interactionType: 'https://schema.org/PlayAction',
        userInteractionCount: gameData.totalPlayers || 0,
      },
    }
  }

  // Generate webpage JSON-LD
  const generateWebPageSchema = (pageData = {}) => {
    const routeMeta = route.meta || {}
    const currentUrl = typeof window !== 'undefined' ? window.location.href : ''

    return {
      '@context': 'https://schema.org',
      '@type': 'WebPage',
      name: pageData.title || routeMeta.title || 'HomesToCompare',
      description:
        pageData.description ||
        routeMeta.description ||
        'Make Smarter Real Estate Choices',
      url: currentUrl,
      isPartOf: {
        '@type': 'WebSite',
        name: 'HomesToCompare - Property Squares',
        url: 'https://www.propertysquares.com',
      },
      publisher: {
        '@type': 'Organization',
        name: 'HomesToCompare',
      },
      dateModified: new Date().toISOString(),
      inLanguage: 'en-GB',
      keywords: routeMeta.keywords || pageData.keywords,
    }
  }

  // Initialize default JSON-LD for all pages
  const initializeDefaultJsonLd = () => {
    // Add organization schema
    addJsonLd(organizationSchema, 'organization-schema')

    // Add website schema
    addJsonLd(websiteSchema, 'website-schema')

    // Add service schema
    addJsonLd(realEstateServiceSchema, 'service-schema')

    // Add webpage schema
    addJsonLd(generateWebPageSchema(), 'webpage-schema')
  }

  // Update webpage schema when route changes
  const updateWebPageSchema = (pageData = {}) => {
    removeJsonLd('webpage-schema')
    addJsonLd(generateWebPageSchema(pageData), 'webpage-schema')
  }

  // Generate breadcrumb JSON-LD
  const addBreadcrumbSchema = (breadcrumbs) => {
    if (breadcrumbs && breadcrumbs.length > 0) {
      addJsonLd(breadcrumbListSchema(breadcrumbs), 'breadcrumb-schema')
    }
  }

  return {
    addJsonLd,
    removeJsonLd,
    clearJsonLd,
    generatePropertySchema,
    generateGameSessionSchema,
    generateWebPageSchema,
    initializeDefaultJsonLd,
    updateWebPageSchema,
    addBreadcrumbSchema,
  }
}
