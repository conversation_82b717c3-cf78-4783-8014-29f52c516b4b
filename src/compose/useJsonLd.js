// Composable for managing JSON-LD structured data
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useMeta } from 'quasar'
import { 
  organizationSchema, 
  websiteSchema, 
  realEstateServiceSchema,
  breadcrumbListSchema,
  gameSchema 
} from 'src/data/static-json-ld.js'

export default function useJsonLd() {
  const route = useRoute()
  const jsonLdScripts = ref([])

  // Helper function to create JSON-LD script element
  const createJsonLdScript = (data, id = null) => {
    if (process.env.SERVER) {
      // For SSR, we'll inject into meta
      return {
        type: 'application/ld+json',
        innerHTML: JSON.stringify(data, null, 2),
        id: id || `json-ld-${Date.now()}`
      }
    } else {
      // For client-side
      const script = document.createElement('script')
      script.type = 'application/ld+json'
      script.innerHTML = JSON.stringify(data, null, 2)
      if (id) script.id = id
      return script
    }
  }

  // Add JSON-LD to page
  const addJsonLd = (data, id = null) => {
    const script = createJsonLdScript(data, id)
    
    if (process.env.SERVER) {
      // Store for SSR injection
      jsonLdScripts.value.push(script)
    } else {
      // Add to DOM on client
      document.head.appendChild(script)
      jsonLdScripts.value.push(script)
    }
  }

  // Remove JSON-LD from page
  const removeJsonLd = (id) => {
    if (!process.env.SERVER) {
      const existingScript = document.getElementById(id)
      if (existingScript) {
        existingScript.remove()
      }
    }
    jsonLdScripts.value = jsonLdScripts.value.filter(script => script.id !== id)
  }

  // Clear all JSON-LD scripts
  const clearJsonLd = () => {
    if (!process.env.SERVER) {
      jsonLdScripts.value.forEach(script => {
        if (script.id && document.getElementById(script.id)) {
          document.getElementById(script.id).remove()
        }
      })
    }
    jsonLdScripts.value = []
  }

  // Generate property-specific JSON-LD
  const generatePropertySchema = (property) => {
    if (!property) return null

    return {
      "@context": "https://schema.org",
      "@type": "RealEstateListing",
      "name": property.title || property.catchy_title || "Property Listing",
      "description": property.description_medium || property.description_short || "Property for sale",
      "url": window.location.href,
      "offers": {
        "@type": "Offer",
        "price": property.price_sale_current_cents ? (property.price_sale_current_cents / 100).toString() : null,
        "priceCurrency": property.currency || "GBP",
        "availability": property.visible ? "https://schema.org/InStock" : "https://schema.org/OutOfStock"
      },
      "address": {
        "@type": "PostalAddress",
        "streetAddress": property.street_address,
        "addressLocality": property.city,
        "addressRegion": property.region,
        "postalCode": property.postal_code,
        "addressCountry": property.country || "GB"
      },
      "geo": property.latitude && property.longitude ? {
        "@type": "GeoCoordinates",
        "latitude": property.latitude,
        "longitude": property.longitude
      } : null,
      "numberOfRooms": property.count_bedrooms,
      "numberOfBathroomsTotal": property.count_bathrooms,
      "floorSize": property.constructed_area ? {
        "@type": "QuantitativeValue",
        "value": property.constructed_area,
        "unitCode": property.area_unit === "sqmt" ? "MTK" : "FTK"
      } : null,
      "yearBuilt": property.year_construction || null,
      "image": property.sale_listing_pics && property.sale_listing_pics.length > 0 
        ? property.sale_listing_pics.map(pic => pic.image_details?.url).filter(Boolean)
        : null
    }
  }

  // Generate game session JSON-LD
  const generateGameSessionSchema = (gameData) => {
    return {
      ...gameSchema(gameData),
      "url": window.location.href,
      "dateCreated": new Date().toISOString(),
      "interactionStatistic": {
        "@type": "InteractionCounter",
        "interactionType": "https://schema.org/PlayAction",
        "userInteractionCount": gameData.totalPlayers || 0
      }
    }
  }

  // Generate webpage JSON-LD
  const generateWebPageSchema = (pageData = {}) => {
    const routeMeta = route.meta || {}
    
    return {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": pageData.title || routeMeta.title || "HomesToCompare",
      "description": pageData.description || routeMeta.description || "Make Smarter Real Estate Choices",
      "url": window.location.href,
      "isPartOf": {
        "@type": "WebSite",
        "name": "HomesToCompare - Property Squares",
        "url": "https://www.propertysquares.com"
      },
      "publisher": {
        "@type": "Organization",
        "name": "HomesToCompare"
      },
      "dateModified": new Date().toISOString(),
      "inLanguage": "en-GB",
      "keywords": routeMeta.keywords || pageData.keywords
    }
  }

  // Initialize default JSON-LD for all pages
  const initializeDefaultJsonLd = () => {
    // Add organization schema
    addJsonLd(organizationSchema, 'organization-schema')
    
    // Add website schema
    addJsonLd(websiteSchema, 'website-schema')
    
    // Add service schema
    addJsonLd(realEstateServiceSchema, 'service-schema')
    
    // Add webpage schema
    addJsonLd(generateWebPageSchema(), 'webpage-schema')
  }

  // Update webpage schema when route changes
  const updateWebPageSchema = (pageData = {}) => {
    removeJsonLd('webpage-schema')
    addJsonLd(generateWebPageSchema(pageData), 'webpage-schema')
  }

  // Generate breadcrumb JSON-LD
  const addBreadcrumbSchema = (breadcrumbs) => {
    if (breadcrumbs && breadcrumbs.length > 0) {
      addJsonLd(breadcrumbListSchema(breadcrumbs), 'breadcrumb-schema')
    }
  }

  // Computed property for SSR meta injection
  const jsonLdMeta = computed(() => {
    if (process.env.SERVER) {
      return jsonLdScripts.value.reduce((acc, script) => {
        acc[script.id] = {
          innerHTML: script.innerHTML,
          type: script.type
        }
        return acc
      }, {})
    }
    return {}
  })

  // Use Quasar's useMeta for SSR support
  if (process.env.SERVER) {
    useMeta(() => ({
      script: jsonLdMeta.value
    }))
  }

  // Cleanup on unmount
  onUnmounted(() => {
    clearJsonLd()
  })

  return {
    addJsonLd,
    removeJsonLd,
    clearJsonLd,
    generatePropertySchema,
    generateGameSessionSchema,
    generateWebPageSchema,
    initializeDefaultJsonLd,
    updateWebPageSchema,
    addBreadcrumbSchema,
    jsonLdScripts: jsonLdScripts.value
  }
}
